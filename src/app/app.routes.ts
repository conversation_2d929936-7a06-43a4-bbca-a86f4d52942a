import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: 'login',
    loadComponent: () => import('./pages/login/login.page').then((m) => m.LoginPage),
  },
  {
    path: 'home',
    loadComponent: () => import('./pages/home/<USER>').then((m) => m.HomePage),
  },
  {
    path: 'invoices',
    loadComponent: () => import('./pages/invoices/invoices').then((m) => m.InvoicesComponent),
  },
  {
    path: '',
    redirectTo: 'login',
    pathMatch: 'full',
  },
];
