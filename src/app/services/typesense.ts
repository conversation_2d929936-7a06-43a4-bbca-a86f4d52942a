import { Injectable } from "@angular/core";
import { environment } from "src/environments/environment";
import { Client } from 'typesense';
@Injectable({
    providedIn: 'root'
})
export class TypeSenseService {
    client: Client | any;
    constructor() {
        this.initiTypesense();
    }

    initiTypesense() {
        this.client = new Client({
            nodes: [{
                host: environment.typesense.host,
                port: Number(environment.typesense.port),
                protocol: environment.typesense.protocol,
            }],
            apiKey: environment.typesense.apiKey,
            connectionTimeoutSeconds: 2,

        });
    }

    async getCategories(store_id: number=936191): Promise<any> {
        try {
            const params = {
                q: '*',
                filter_by: `store_id:=${store_id} && status:=true && parent_id:=0`
            }
            const response = await this.client.collections('store_categories').documents().search(params)
            return response.hits?.map((hit: any) => hit.document) || [];
        } catch (error) {
            console.log(error);
        }
    }
 
    async getProductsByCategory(categoryName: string, page: number = 1, perPage: number = 20, store_id: number = 936191): Promise<any> {
        try {
            const params = {
                q: '*',
                filter_by: `store_id:=${store_id} && category_id:=${categoryName} && is_available:=true`,
                include_fields: 'ean_number,sku,thumbnail_url,price,mrp,name',
                page: page,
                per_page: perPage
            }
            const response = await this.client.collections('store_products').documents().search(params)
            
            return {
                products: response.hits?.map((hit: any) => hit.document) || [],
                totalPages: Math.ceil(response.found / perPage),
                currentPage: page,
                totalProducts: response.found
            };
        } catch (error) {
            console.log(error);
            return {
                products: [],
                totalPages: 0,
                currentPage: 1,
                totalProducts: 0
            };
        }
    }
    async searchProducts(query: string, page: number = 1, perPage: number = 20, store_id: number = 936191): Promise<any> {
        try {
            const params = {
                q: query,
                query_by: 'name,description,sku',
                filter_by: `store_id:=${store_id} && status:=true`,
                page: page,
                per_page: perPage
            }
            const response = await this.client.collections('products').documents().search(params)
            return {
                products: response.hits?.map((hit: any) => hit.document) || [],
                totalPages: Math.ceil(response.found / perPage),
                currentPage: page,
                totalProducts: response.found
            };
        } catch (error) {
            console.log(error);
            return {
                products: [],
                totalPages: 0,
                currentPage: 1,
                totalProducts: 0
            };
        }
    }

    async getAllProducts(page: number = 1, perPage: number = 20, store_id: number = 936191): Promise<any> {
        try {
            const params = {
                q: '*',
                filter_by: `store_id:=${store_id} && status:=true`,
                page: page,
                per_page: perPage
            }
            const response = await this.client.collections('store_products').documents().search(params)
            return {
                products: response.hits?.map((hit: any) => hit.document) || [],
                totalPages: Math.ceil(response.found / perPage),
                currentPage: page,
                totalProducts: response.found
            };
        } catch (error) {
            console.log(error);
            return {
                products: [],
                totalPages: 0,
                currentPage: 1,
                totalProducts: 0
            };
        }
    }
    // to display the order items with sku from typesense in invoices page billing section
    async getProductBySku(sku: string, store_id: number = 936191): Promise<any> {
        try {
            let params = {
                q: sku,
                query_by: 'sku',
                filter_by: `store_id:= ${store_id} && sku:=${sku}`,
                include_fields: 'ean_number,thumbnail_url,price,mrp,name',
                page: 1,
                per_page: 20
            }
            let response = await this.client.collections('products').documents().search(params);
            let products = response.hits?.map((hit: any) => hit.document) || [];
            const result = products.length > 0 ? products[0] : null;
            return result;
        } catch (error) {
            console.log('Error fetching product by SKU:', error);
            return null;
        }
    }
};