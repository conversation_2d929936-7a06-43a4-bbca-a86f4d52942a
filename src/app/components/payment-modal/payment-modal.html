<p-dialog 
  [header]="title" 
  [(visible)]="visible" 
  [modal]="true" 
  [style]="{width: '800px' }"
  [closable]="!isProcessing"
  (onHide)="onDialogHide()">
  
  <div class="flex flex-col gap-4 p-4 text-md">
    <div class="payment-methods">
      <h6 class="text-lg mb-2">Select Payment Method:</h6>
      
      <!-- Payment Method Selection -->
      <div class="flex flex-col gap-2 mb-6 border-b border-gray-200 pb-5 text-md">
        <div class="flex items-center gap-2">
          <p-radioButton
            name="paymentMethod"
            value="cash"
            [(ngModel)]="selectedPaymentMethod"
            (onClick)="handlePaymentMethodChange('cash')"
            styleClass="custom-radio-blue">
          </p-radioButton>
          <label class="text-lg cursor-pointer" (click)="handlePaymentMethodChange('cash')">Cash</label>
        </div>
        <div class="flex items-center gap-2">
          <p-radioButton
            name="paymentMethod"
            value="upi"
            [(ngModel)]="selectedPaymentMethod"
            (onClick)="handlePaymentMethodChange('upi')"
            styleClass="custom-radio-blue">
          </p-radioButton>
          <label class="text-lg  cursor-pointer" (click)="handlePaymentMethodChange('upi')">UPI</label>
        </div>
        <div class="flex items-center gap-2">
          <p-radioButton
            name="paymentMethod"
            value="card"
            [(ngModel)]="selectedPaymentMethod"
            (onClick)="handlePaymentMethodChange('card')"
            styleClass="custom-radio-blue">
          </p-radioButton>
          <label class="text-lg cursor-pointer" (click)="handlePaymentMethodChange('card')">Card</label>
        </div>
        <div class="flex items-center gap-2">
          <p-radioButton
            name="paymentMethod"
            severity="info" 
            value="partial"
            [(ngModel)]="selectedPaymentMethod"
            (onClick)="handlePaymentMethodChange('partial')"
           >
          </p-radioButton>
          <label class="text-lg  cursor-pointer" (click)="handlePaymentMethodChange('partial')">Partial Payment</label>
        </div>
      </div>

      <!-- Payment Details -->
      <div class="flex flex-col gap-3">
        <!-- Cash Payment -->
        <div *ngIf="selectedPaymentMethod === 'cash'" class="flex flex-col gap-2">
          <label class="text-md font-medium">Enter Cash Amount:</label>
          <p-inputNumber 
             severity="info"
            [(ngModel)]="cashAmount" 
            [min]="0" 
            [max]="totalAmount * 2"
            mode="currency" 
            currency="INR" 
            [showButtons]="true"
            class="w-full h-[60px]">
          </p-inputNumber>
          <small class="text-gray-500" *ngIf="getChange() >= 0">
            Change: ₹{{getChange().toFixed(2)}}
          </small>
          <small class="text-red-500" *ngIf="getChange() < 0">
            Insufficient amount: ₹{{Math.abs(getChange()).toFixed(2)}} short
          </small>
        </div>

        <!-- UPI Payment -->
        <div *ngIf="selectedPaymentMethod === 'upi'" class="flex flex-col gap-2">
          <label class="text-md font-medium">Enter UPI Amount:</label>
          <p-inputNumber 
            [(ngModel)]="upiAmount" 
            [min]="0" 
            [max]="totalAmount"
            mode="currency" 
            currency="INR"
            class="w-full h-[60px]">
          </p-inputNumber>
          <label class="text-md font-medium">UPI ID:</label>
          <input 
            type="text" 
            pInputText 
            placeholder="Enter UPI ID (e.g., user@paytm)" 
            class="w-full p-2 h-[60px]" 
            [(ngModel)]="upiId">
        </div>

        <!-- Card Payment -->
        <div *ngIf="selectedPaymentMethod === 'card'" class="flex flex-col gap-2">
          <label class="text-lg font-medium">Card Type:</label>
          <div class="flex items-center gap-4">
            <div class="flex items-center gap-2">
              <p-radioButton name="cardType" value="credit" [(ngModel)]="cardType" styleClass="custom-radio-blue"></p-radioButton>
              <label class="text-base cursor-pointer" (click)="cardType = 'credit'">Credit Card</label>
            </div>
            <div class="flex items-center gap-2">
              <p-radioButton name="cardType" value="debit" [(ngModel)]="cardType" styleClass="custom-radio-blue"></p-radioButton>
              <label class="text-sm cursor-pointer" (click)="cardType = 'debit'">Debit Card</label>
            </div>
          </div>
          <label class="text-md font-medium ">Card Amount:</label>
          <p-inputNumber 
            [(ngModel)]="cardAmount" 
            [min]="0" 
            [max]="totalAmount"
            mode="currency" 
            currency="INR"
            class="w-full h-[60px]">
          </p-inputNumber>
        </div>

        <!-- Partial Payment -->
        <div *ngIf="selectedPaymentMethod === 'partial'" class="flex flex-row gap-4">
          <label class="text-lg font-medium">Cash Payment</label>
          <p-inputNumber 
            [(ngModel)]="partialPaymentAmount" 
            [min]="0" 
            [max]="totalAmount"
            mode="currency" 
            currency="INR"
            (onInput)="updatePartialPayment($event.value || 0)"
            class="w-full h-[60px] ">
          </p-inputNumber>
          <div class="bg-gray-50 p-2 rounded">
            <span class="text-base ">Remaining Amount (Card): <span class="text-cyan-600 font-bold">₹{{remainingAmount.toFixed(2)}}</span></span>
          </div>
        </div>
      </div>

      <!-- Total Amount Display -->
      <div class="mt-4 p-3 bg-blue-50 rounded-lg">
        <div class="flex justify-between items-center">
          <span class="text-lg font-medium">Total Amount:</span>
          <span class="text-2xl font-bold text-blue-600">₹{{totalAmount.toFixed(2)}}</span>
        </div>
      </div>
    </div>

    <!-- Confirmation Text -->
    <p class="text-sm text-gray-600">{{confirmText}}</p>

    <!-- Action Buttons -->
    <div class="flex justify-end gap-2 pt-2">
      <button 
        pButton  
        severity="danger"
        [label]="cancelButtonLabel" 
        class="p-button-text text-cyan-600" 
        (click)="onCancel()"
        [disabled]="isProcessing">
      </button>
      <button 
        pButton 
        [label]="confirmButtonLabel" 
        severity="info" 
        (click)="onConfirm()"
        [loading]="isProcessing"
        [disabled]="isProcessing">
      </button>
    </div>
  </div>
</p-dialog>
